jQuery(document).ready(function($) {
    console.log('🔧 SAP Debug: Single product SAP debugging initialized');
    
    // Check if we're on a single product page
    if (!$('body').hasClass('single-product')) {
        console.log('ℹ️ SAP Debug: Not on single product page, skipping SAP debug');
        return;
    }
    
    // Get product data
    var productId = $('input[name="add-to-cart"]').val() || $('button[name="add-to-cart"]').val();
    var productSku = $('.sku').text() || $('.product_meta .sku_wrapper .sku').text();
    
    console.log('📦 SAP Debug: Product detected', {
        productId: productId,
        productSku: productSku
    });
    
    // Create debug panel for single product
    function createSingleProductDebugPanel() {
        if ($('#sap-single-debug').length > 0) {
            return;
        }
        
        var debugHtml = `
            <div id="sap-single-debug" style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 400px;
                max-height: 80vh;
                overflow-y: auto;
                background: #fff;
                border: 2px solid #007cba;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 13px;
            ">
                <div style="
                    background: #007cba;
                    color: white;
                    padding: 12px 15px;
                    font-weight: 600;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>🔧 SAP Debug Panel</span>
                    <button id="sap-debug-close" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 0;
                        width: 24px;
                        height: 24px;
                    ">×</button>
                </div>
                <div id="sap-debug-content" style="padding: 15px;">
                    <div style="margin-bottom: 15px;">
                        <strong>Product Info:</strong><br>
                        ID: ${productId || 'N/A'}<br>
                        SKU: ${productSku || 'N/A'}
                    </div>
                    <div style="margin-bottom: 15px;">
                        <button id="test-sap-pricing" style="
                            background: #007cba;
                            color: white;
                            border: none;
                            padding: 8px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">Test SAP Pricing</button>
                        <button id="clear-debug-log" style="
                            background: #666;
                            color: white;
                            border: none;
                            padding: 8px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                            margin-left: 8px;
                        ">Clear Log</button>
                    </div>
                    <div id="sap-debug-log" style="
                        background: #f8f9fa;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        padding: 10px;
                        max-height: 300px;
                        overflow-y: auto;
                        font-family: monospace;
                        font-size: 11px;
                        line-height: 1.4;
                    ">
                        <div style="color: #666;">Debug log will appear here...</div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(debugHtml);
        
        // Close button functionality
        $('#sap-debug-close').on('click', function() {
            $('#sap-single-debug').remove();
        });
        
        // Clear log button
        $('#clear-debug-log').on('click', function() {
            $('#sap-debug-log').html('<div style="color: #666;">Debug log cleared...</div>');
        });
        
        // Test SAP pricing button
        $('#test-sap-pricing').on('click', function() {
            testSapPricing();
        });
    }
    
    // Function to add debug messages to the panel
    function addDebugMessage(type, message, data) {
        var timestamp = new Date().toLocaleTimeString();
        var color = type === 'error' ? '#d32f2f' : type === 'warning' ? '#f57c00' : type === 'success' ? '#388e3c' : '#1976d2';
        var icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
        
        var debugHtml = `
            <div style="margin-bottom: 8px; padding: 8px; border-left: 3px solid ${color}; background: ${color}10;">
                <div style="font-weight: 600; color: ${color};">
                    ${icon} ${timestamp} - ${message}
                </div>
                ${data ? `<pre style="margin: 4px 0 0 0; font-size: 10px; color: #666; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>` : ''}
            </div>
        `;
        
        $('#sap-debug-log').append(debugHtml);
        $('#sap-debug-log').scrollTop($('#sap-debug-log')[0].scrollHeight);
    }
    
    // Function to test SAP pricing for current product
    function testSapPricing() {
        if (!productSku) {
            addDebugMessage('error', 'No product SKU found');
            return;
        }
        
        addDebugMessage('info', 'Testing SAP pricing for SKU: ' + productSku);
        
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            dataType: 'text',
            data: {
                action: 'get_sap_pricing_bulk',
                skus: [productSku]
            },
            beforeSend: function() {
                addDebugMessage('info', 'Sending SAP pricing request...');
            },
            success: function(responseText) {
                addDebugMessage('info', 'Raw response received', {responseText: responseText.substring(0, 500) + '...'});
                
                // Clean and parse response
                var jsonStart = responseText.indexOf('{');
                if (jsonStart > 0) {
                    responseText = responseText.substring(jsonStart);
                    addDebugMessage('info', 'Response cleaned');
                }
                
                var response;
                try {
                    response = JSON.parse(responseText);
                    addDebugMessage('success', 'Response parsed successfully', response);
                } catch (e) {
                    addDebugMessage('error', 'JSON parse error', {error: e.message, responseText: responseText});
                    return;
                }
                
                if (response.success && response.data && response.data.pricing && response.data.pricing[productSku]) {
                    var sapData = response.data.pricing[productSku];
                    addDebugMessage('success', 'SAP pricing found for product', sapData);
                } else {
                    addDebugMessage('warning', 'No SAP pricing data found for this product', response);
                }
            },
            error: function(xhr, status, error) {
                addDebugMessage('error', 'AJAX request failed', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
            }
        });
    }
    
    // Auto-create debug panel if URL contains debug parameter
    if (window.location.search.includes('sap_debug=1') || window.location.hash.includes('sap_debug')) {
        createSingleProductDebugPanel();
        addDebugMessage('info', 'SAP Debug panel auto-opened');
    }
    
    // Keyboard shortcut to toggle debug panel (Ctrl+Shift+S)
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.keyCode === 83) { // Ctrl+Shift+S
            e.preventDefault();
            if ($('#sap-single-debug').length > 0) {
                $('#sap-single-debug').remove();
            } else {
                createSingleProductDebugPanel();
                addDebugMessage('info', 'Debug panel opened via keyboard shortcut');
            }
        }
    });
    
    // Log initial product info to console
    console.log('🏷️ SAP Debug: Single product page loaded', {
        productId: productId,
        productSku: productSku,
        debugTip: 'Press Ctrl+Shift+S to open debug panel, or add ?sap_debug=1 to URL'
    });
});
