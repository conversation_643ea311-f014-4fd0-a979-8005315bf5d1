jQuery(document).ready(function($) {
    // Populate the custom field dropdowns
    for (const [key, values] of Object.entries(customFilterData)) {
        // console.log(key);
        // console.log(values);
        $.each(values, function(index, value) {
            $(`#${key}`).append(new Option(value, value));
        })
    }

    // Initialize Select2 on filter dropdowns
    $('.filter_meta_options').select2({
        placeholder: 'Select options...',
        allowClear: true,
        // width: '100%',
        closeOnSelect: false,
        tags: false,
        // dropdownAutoWidth: true,
        maximumSelectionLength: 10 // Limit to 10 selections to prevent UI issues
    });

    $('.filter_meta_options').on('click', function(){
    });
    // Initialize DataTable
    var table = $('#products-table').DataTable({
        serverSide: true,
        ajax: {
            url: ajax_object.ajax_url,
            type: 'POST',
            data: function(d) {
                d.action = 'fetch_products';
                d.search.value = $('#custom-search').val();
                d.customFields = {
                    brand: $('#brand').val(), 
                    product_line: $('#product_line').val(),
                    product_family: $('#product_family').val(),
                    model_size: $('#model_size').val(),
                    ce_approved: $('#ce_approved').val(),
                };
                d.inStockOnly = $('#in-stock').is(':checked');
                d.sortBy = $('#sort-by').val();
            },
            beforeSend: function() {
                $('#products-table-wrap').addClass('bg--loading');
            },
            complete: function() {
                $('#products-table-wrap').removeClass('bg--loading');
            }
        },
        columns: [
            { data: 'quantity', orderable: false, width: '110px' },
            { data: 'title' },
            { data: 'stock' },
            { data: 'price' },
            {
                data: 'net_price',
                orderable: false,
                defaultContent: '<div class="sap-net-price-loading">Loading...</div>'
            },
            { data: 'brand' },
            { data: 'product_line' },
            { data: 'product_family' },
            { data: 'model_size' },
            { data: 'ce_approver' }
        ],
        paging: true,
        pageLength: 10,
        lengthMenu: [10, 25],
        searching: false,
        language: {
            search: "Filter products:",
            lengthMenu: "Show _MENU_ products",
            info: "<button id='add-selected-to-cart' class='orange-bg white w100 no-border f-16 py-1 mr-2 px-2 add_all_to_cart_button normal' type='button'>Add To Cart</button>Showing _START_-_END_ of _TOTAL_",
            paginate: {
                first: "<<",
                last: ">>",
                next: ">",
                previous: "<"
            }
        },
        ordering: true,
        responsive: true,
        lengthChange: true,  // Enable "per page" dropdown
        dom: '<"top"lf>rt<"bottom"ip><"clear">',
        // lengthChange: false,  // Hide "per page" dropdown
        // dom: 'rtip',  // Only show table, pagination, and information, remove length menu
    });
    table.on('draw', function() {
        var info = table.page.info();
        const recordsTotalFormated = info.recordsTotal.toLocaleString('en-US');
        var infoHtml = `SHOWING ${info.start + 1} TO ${info.end} of ${recordsTotalFormated}`;

        // Update the custom location next to the "Sort by" dropdown
        $('#pagination-info').html(infoHtml);

        // Fetch SAP pricing for all visible products (with small delay to ensure DOM is ready)
        setTimeout(function() {
            fetchSapPricingForTable();
        }, 100);
    });
    // Event listener for the "Apply All" button
    $('#apply-filters').on('click', function() {
        table.ajax.reload();
    });

    // Function to fetch SAP pricing for all visible products in the table
    function fetchSapPricingForTable() {
        //console.log('fetchSapPricingForTable called');

        // Collect all SKUs from the current page
        var skus = [];
        var totalRows = $('#products-table tbody tr').length;

        //console.log('Total visible rows found:', totalRows);

        // Look for SKUs in the table rows - they appear in the "Materials #" column
        var rowsWithSku = 0;
        var rowsWithoutSku = 0;

        $('#products-table tbody tr').each(function(index) {
            var $row = $(this);
            // The SKU appears to be in the second column (Materials #)
            var $titleCell = $row.find('td:nth-child(2)');
            var titleText = $titleCell.text();

            // Extract SKU from the title text (it appears to be the first line)
            var lines = titleText.trim().split('\n');
            var sku = '';

            // Look for a line that looks like a SKU (numbers or alphanumeric)
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                // Match numeric SKUs (like 100065) or alphanumeric SKUs (like C2514CBT, C158C, 16664-4)
                if (line.match(/^[A-Z0-9-]+$/i) && line.length >= 3) {
                    sku = line;
                    break;
                }
            }

            if (sku) {
                skus.push(sku);
                rowsWithSku++;
                // Add the SKU as a data attribute to the net price cell for later reference
                $row.find('td:nth-child(5)').attr('data-sku', sku);
            } else {
                rowsWithoutSku++;
                // Debug: log first few rows that don't have valid SKUs
                if (rowsWithoutSku <= 5) {
                    /*console.log('Row', index + 1, 'no SKU found.');
                    console.log('  Title text length:', titleText.length);
                    console.log('  Title text:', JSON.stringify(titleText));
                    console.log('  Lines:', lines);
                    console.log('  HTML content:', $titleCell.html());*/
                }
            }
        });

        //console.log('Rows with SKU:', rowsWithSku, 'Rows without SKU:', rowsWithoutSku);

        if (skus.length === 0) {
            console.log('🔍 SAP Debug: No SKUs found in table');
            return;
        }

        console.log('🚀 SAP Debug: Fetching SAP pricing for SKUs:', skus);

        // Check if we're debugging the specific SKU
        if (skus.includes('202817')) {
            console.log('🎯 DEBUGGING SKU 202817 - This request includes your target SKU!');
            console.log('🎯 Watch for "DEBUGGING SKU 202817 - FULL PAYLOAD" in the console logs below');
        }

        // Prepare request data
        var requestData = {
            action: 'get_sap_pricing_bulk',
            skus: skus
        };

        // Add debug info to page with full request details
        updateDebugInfo('REQUEST', {
            url: ajax_object.ajax_url,
            method: 'POST',
            data: requestData,
            timestamp: new Date().toISOString()
        });

        console.log('📤 SAP Debug: Full request being sent:', {
            url: ajax_object.ajax_url,
            method: 'POST',
            data: requestData,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
        });

        // Make AJAX call to get SAP pricing for all SKUs
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            dataType: 'text', // Get raw text to handle JSON parsing manually
            data: requestData,
            success: function(responseText) {
                console.log('📥 SAP Debug: Raw response received:', responseText);

                // Clean the response - remove any HTML that might be prepended
                var jsonStart = responseText.indexOf('{');
                if (jsonStart > 0) {
                    responseText = responseText.substring(jsonStart);
                    console.log('🧹 SAP Debug: Cleaned response:', responseText);
                }

                // Parse JSON manually
                var response;
                try {
                    response = JSON.parse(responseText);
                } catch (e) {
                    console.error('❌ SAP Debug: JSON parse error:', e);
                    updateDebugInfo('PARSE_ERROR', {error: e.message, responseText: responseText.substring(0, 500)});
                    $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">Parse Error</span>');
                    return;
                }

                console.log('✅ SAP Debug: Parsed response:', response);
                updateDebugInfo('RESPONSE', response);

                // Show debug messages immediately
                if (response.success && response.data && response.data.debug_messages) {
                    console.log('🔧 SAP Debug: Backend debug messages:', response.data.debug_messages);
                    updateDebugInfo('DEBUG_MESSAGES', response.data.debug_messages);
                }

                // Log debug info if available
                if (response.success && response.data && response.data.debug_info) {
                    console.log('🔍 SAP Debug: Backend debug info:', response.data.debug_info);
                    updateDebugInfo('DEBUG_INFO', response.data.debug_info);

                    // Special handling for SKU 202817
                    if (response.data.debug_info['202817']) {
                        console.log('🎯 SKU 202817 DEBUG STATUS:', response.data.debug_info['202817']);
                    }
                }

                // Log detailed debug info with SAP payloads
                if (response.success && response.data && response.data.detailed_debug) {
                    console.log('🔍 SAP Debug: Detailed backend debug info:', response.data.detailed_debug);
                    updateDebugInfo('DETAILED_DEBUG', response.data.detailed_debug);

                    // Special handling for SKU 202817 - show the full payload
                    if (response.data.detailed_debug['202817']) {
                        var debug202817 = response.data.detailed_debug['202817'];
                        console.log('🎯🎯🎯 FULL SAP PAYLOAD FOR SKU 202817 🎯🎯🎯');
                        console.log('Product Info:', {
                            product_found: debug202817.product_found,
                            product_id: debug202817.product_id,
                            product_name: debug202817.product_name
                        });

                        if (debug202817.sap_payload) {
                            console.log('🎯 SAP PAYLOAD OBJECT:', debug202817.sap_payload);
                            console.log('🎯 SAP PAYLOAD JSON:');
                            console.log(debug202817.sap_payload_json);
                        }

                        if (debug202817.SPECIAL_DEBUG_202817) {
                            console.log('🎯 SPECIAL DEBUG INFO:', debug202817.SPECIAL_DEBUG_202817);
                        }

                        // Store the JSON for the copy button
                        if (debug202817.sap_payload_json) {
                            $('#latest-json-payload').text(debug202817.sap_payload_json);
                        }
                    }
                }

                if (response.success && response.data && response.data.pricing) {
                    console.log('💰 SAP Debug: Processing pricing data for', Object.keys(response.data.pricing).length, 'products');

                    // Update each product's net price using the data-sku attribute
                    $('#products-table tbody tr').each(function() {
                        var $row = $(this);
                        var $netPriceCell = $row.find('td:nth-child(5)');
                        var sku = $netPriceCell.data('sku');

                        if (sku && response.data.pricing[sku]) {
                            var sapData = response.data.pricing[sku];
                            console.log('💲 SAP Debug: Updating price for SKU:', sku, 'Data:', sapData);

                            var netPriceHtml = '<div class="sap-table-pricing">';

                            if (sapData.formatted_net_value) {
                                netPriceHtml += '<span class="sap-net-price" style="font-weight: 600; color: #007cba;">' +
                                               sapData.formatted_net_value + '</span>';
                            } else {
                                netPriceHtml += '<span style="color: #666;">N/A</span>';
                            }

                            netPriceHtml += '</div>';
                            $netPriceCell.html(netPriceHtml);
                        } else {
                            if (sku) {
                                console.log('⚠️ SAP Debug: No pricing data for SKU:', sku);
                            }
                            $netPriceCell.html('<span style="color: #666;">N/A</span>');
                        }
                    });
                } else {
                    console.error('❌ SAP Debug: Invalid response format or no pricing data');
                    // Handle error - show N/A for all
                    $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">N/A</span>');
                }
            },
            error: function(xhr, status, error) {
                console.error('🚨 SAP Debug: Failed to fetch SAP pricing:', error);
                updateDebugInfo('ERROR', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">Error</span>');
            }
        });
    }

    // Function to update debug information
    function updateDebugInfo(type, data) {
        // Create debug container if it doesn't exist
        if ($('#sap-debug-info').length === 0) {
            $('#products-table-wrap').after('<div id="sap-debug-info" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"><h4>SAP Pricing Debug Info <button id="copy-json-payload" style="float: right; background: #007cba; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 11px; cursor: pointer; margin-left: 5px;">Copy JSON</button><button id="clear-debug-info" style="float: right; background: #666; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 11px; cursor: pointer;">Clear</button></h4><div id="latest-json-payload" style="display: none;"></div></div>');

            // Add clear button functionality
            $('#clear-debug-info').on('click', function() {
                $('#sap-debug-info').find('div:not(:first):not(#latest-json-payload)').remove();
            });

            // Add copy JSON functionality
            $('#copy-json-payload').on('click', function() {
                var jsonPayload = $('#latest-json-payload').text();
                if (jsonPayload) {
                    navigator.clipboard.writeText(jsonPayload).then(function() {
                        alert('JSON payload copied to clipboard!');
                    }).catch(function() {
                        // Fallback for older browsers
                        var textArea = document.createElement('textarea');
                        textArea.value = jsonPayload;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        alert('JSON payload copied to clipboard!');
                    });
                } else {
                    alert('No JSON payload available to copy. Make sure to trigger a SAP request first.');
                }
            });
        }

        var timestamp = new Date().toLocaleTimeString();
        var backgroundColor = type === 'ERROR' ? '#ffebee' :
                             type === 'REQUEST' ? '#e3f2fd' :
                             type === 'DEBUG_INFO' ? '#fff3e0' :
                             '#e8f5e8';
        var borderColor = type === 'ERROR' ? '#f44336' :
                         type === 'REQUEST' ? '#2196f3' :
                         type === 'DEBUG_INFO' ? '#ff9800' :
                         '#4caf50';

        var debugHtml = '<div style="margin: 10px 0; padding: 10px; background: ' + backgroundColor + '; border-left: 4px solid ' + borderColor + ';">';
        debugHtml += '<strong>' + type + ' (' + timestamp + '):</strong><br>';

        // Special formatting for JSON payloads
        if (type === 'REQUEST' && data.data) {
            debugHtml += '<div style="margin: 8px 0;"><strong>🚀 Frontend Request:</strong></div>';
            debugHtml += '<pre style="margin: 5px 0; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px; white-space: pre-wrap;">' + JSON.stringify(data, null, 2) + '</pre>';
        } else if (type === 'DEBUG_INFO') {
            debugHtml += '<div style="margin: 8px 0;"><strong>🔍 Backend Processing Info:</strong></div>';
            debugHtml += '<pre style="margin: 5px 0; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px; white-space: pre-wrap;">' + JSON.stringify(data, null, 2) + '</pre>';

            // Store the latest JSON payload for copying (look for payload data in debug info)
            if (data && typeof data === 'object') {
                for (var key in data) {
                    if (data[key] && data[key].payload_json_formatted) {
                        $('#latest-json-payload').text(data[key].payload_json_formatted);
                        break;
                    }
                }
            }
        } else if (type === 'DETAILED_DEBUG') {
            debugHtml += '<div style="margin: 8px 0;"><strong>🎯 Detailed SAP Payload Debug:</strong></div>';

            // Special formatting for detailed debug with SAP payloads
            if (data && data['202817'] && data['202817'].sap_payload_json) {
                debugHtml += '<div style="margin: 8px 0; padding: 8px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                debugHtml += '<strong>🎯 SKU 202817 - FULL SAP PAYLOAD:</strong><br>';
                debugHtml += '<pre style="margin: 5px 0; font-size: 10px; background: #fff; padding: 8px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + data['202817'].sap_payload_json + '</pre>';
                debugHtml += '</div>';

                // Store this payload for copying
                $('#latest-json-payload').text(data['202817'].sap_payload_json);
            }

            debugHtml += '<pre style="margin: 5px 0; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;">' + JSON.stringify(data, null, 2) + '</pre>';
        } else if (type === 'DEBUG_MESSAGES') {
            debugHtml += '<div style="margin: 8px 0;"><strong>🔧 Backend Debug Messages:</strong></div>';
            if (Array.isArray(data)) {
                debugHtml += '<ul style="margin: 5px 0; padding-left: 20px; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px;">';
                data.forEach(function(message) {
                    debugHtml += '<li style="margin: 2px 0;">' + message + '</li>';
                });
                debugHtml += '</ul>';
            } else {
                debugHtml += '<pre style="margin: 5px 0; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px; white-space: pre-wrap;">' + JSON.stringify(data, null, 2) + '</pre>';
            }
        } else {
            debugHtml += '<pre style="margin: 5px 0; font-size: 11px; background: #f0f0f0; padding: 8px; border-radius: 3px; white-space: pre-wrap;">' + JSON.stringify(data, null, 2) + '</pre>';
        }

        debugHtml += '</div>';

        $('#sap-debug-info').append(debugHtml);

        // Keep only last 8 debug entries (increased for more visibility)
        var debugEntries = $('#sap-debug-info > div');
        if (debugEntries.length > 9) { // 9 because we have the h4 title
            debugEntries.first().next().remove();
        }

        // Auto-scroll to bottom of debug info
        $('#sap-debug-info').scrollTop($('#sap-debug-info')[0].scrollHeight);
    }
});
