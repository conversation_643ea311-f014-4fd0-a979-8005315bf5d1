jQuery(document).ready(function($) {
    console.log('Cart SAP Pricing script loaded');

    // Function to replace cart prices with SAP pricing
    function updateCartWithSapPricing() {
        console.log('updateCartWithSapPricing called');

        // Debug: Check what cart elements exist
        console.log('Cart form exists:', $('.woocommerce-cart-form').length);
        console.log('Cart tbody exists:', $('.woocommerce-cart-form tbody').length);
        console.log('Cart tbody tr count:', $('.woocommerce-cart-form tbody tr').length);
        console.log('All tbody tr count:', $('tbody tr').length);
        console.log('All tr count:', $('tr').length);

        // Try different selectors to find cart rows
        var possibleSelectors = [
            '.woocommerce-cart-form tbody tr',
            'tbody tr',
            '.cart_item',
            'tr.cart_item',
            '.woocommerce-cart-form tr',
            'form[action*="cart"] tbody tr'
        ];

        possibleSelectors.forEach(function(selector) {
            var count = $(selector).length;
            console.log('Selector "' + selector + '" found:', count, 'elements');
        });

        // Debug: Look for cart-related elements
        console.log('WC Block cart:', $('.wc-block-cart').length);
        console.log('WC Block cart items:', $('.wc-block-cart-items').length);
        console.log('WC Block cart items table:', $('.wc-block-cart-items.wp-block-woocommerce-cart-line-items-block').length);
        console.log('WC Block cart rows:', $('.wc-block-cart-items__row').length);
        console.log('Cart table:', $('table.cart').length);
        console.log('Shop table:', $('table.shop_table').length);
        console.log('Elements with "cart" in class:', $('[class*="cart"]').length);
        console.log('Elements with "product" in class:', $('[class*="product"]').length);

        // Debug: Look at the page structure
        console.log('Forms on page:', $('form').length);
        $('form').each(function(index) {
            var $form = $(this);
            console.log('Form', index, ':', {
                action: $form.attr('action'),
                class: $form.attr('class'),
                id: $form.attr('id'),
                hasTable: $form.find('table').length > 0
            });
        });

        // Collect all cart items and their SKUs (Classic WooCommerce cart structure)
        var cartItems = [];

        $('.woocommerce-cart-form tbody tr').each(function() {
            var $row = $(this);
            var $nameCell = $row.find('.product-name');
            var $priceCell = $row.find('.product-price');
            var $subtotalCell = $row.find('.product-subtotal');
            var $quantityInput = $row.find('.qty');

            // Debug: log what we find in each row
            console.log('Row found:', {
                hasNameCell: $nameCell.length > 0,
                hasPriceCell: $priceCell.length > 0,
                hasQuantityInput: $quantityInput.length > 0,
                nameText: $nameCell.text(),
                nameHtml: $nameCell.html()
            });

            // Extract SKU from classic WooCommerce cart product name
            var productText = $nameCell.text();
            var sku = '';

            // Look for SKU in the product name text
            var lines = productText.trim().split('\n');
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                // Match numeric SKUs (like 100065) or alphanumeric SKUs (like C2514CBT)
                if (line.match(/^[A-Z0-9]+$/i) && line.length >= 3) {
                    sku = line;
                    break;
                }
            }

            console.log('SKU extraction result:', {
                productText: productText,
                extractedSku: sku
            });

            if (sku && $quantityInput.length > 0) {
                var quantity = $quantityInput.val() ? parseInt($quantityInput.val()) : 1;
                cartItems.push({
                    sku: sku,
                    quantity: quantity,
                    priceCell: $priceCell,
                    subtotalCell: $subtotalCell,
                    row: $row
                });
            }
        });
        
        console.log('🛒 SAP Debug: Found cart items:', cartItems);

        if (cartItems.length === 0) {
            console.log('⚠️ SAP Debug: No cart items with SKUs found');
            return;
        }
        
        // Extract just the SKUs for the API call
        var skus = cartItems.map(function(item) { return item.sku; });
        
        // Make AJAX call to get SAP pricing for all cart SKUs
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            dataType: 'text',
            data: {
                action: 'get_sap_pricing_bulk',
                skus: skus
            },
            success: function(responseText) {
                console.log('📥 SAP Debug: Cart SAP pricing response:', responseText);

                // Clean and parse response
                var jsonStart = responseText.indexOf('{');
                if (jsonStart > 0) {
                    responseText = responseText.substring(jsonStart);
                    console.log('🧹 SAP Debug: Cleaned cart response:', responseText);
                }

                var response;
                try {
                    response = JSON.parse(responseText);
                } catch (e) {
                    console.error('❌ SAP Debug: Cart SAP JSON parse error:', e);
                    return;
                }

                console.log('✅ SAP Debug: Parsed cart response:', response);
                
                if (response.success && response.data && response.data.pricing) {
                    // Update each cart item with SAP pricing
                    cartItems.forEach(function(item) {
                        if (response.data.pricing[item.sku]) {
                            var sapData = response.data.pricing[item.sku];
                            
                            if (sapData.net_value) {
                                // Convert SAP response (divide by 100)
                                var netValueDecimal = parseFloat(sapData.net_value) / 100;
                                var unitPrice = netValueDecimal / item.quantity;
                                
                                // Format currency (assuming EUR for now - can be enhanced)
                                var formattedUnitPrice = '€' + unitPrice.toFixed(2);
                                var formattedSubtotal = '€' + netValueDecimal.toFixed(2);

                                // Update classic WooCommerce cart price elements
                                item.priceCell.html(formattedUnitPrice + '<br><small style="color: #999;">SAP NET</small>');
                                item.subtotalCell.html(formattedSubtotal + '<br><small style="color: #999;">SAP NET</small>');
                                
                                console.log('💰 SAP Debug: Updated', item.sku, 'with SAP pricing:', formattedUnitPrice, '/', formattedSubtotal);
                            }
                        }
                    });
                } else {
                    console.log('⚠️ SAP Debug: Cart SAP pricing - Invalid response format');
                }
            },
            error: function(xhr, status, error) {
                console.error('Cart SAP pricing AJAX error:', error);
            }
        });
    }
    
    // Function to wait for WooCommerce Blocks to load
    function waitForWooCommerceBlocks(callback, maxAttempts = 10, attempt = 1) {
        console.log('Waiting for WooCommerce Blocks, attempt:', attempt);

        if ($('.wc-block-cart-items__row').length > 0) {
            console.log('WooCommerce Blocks loaded, running callback');
            callback();
        } else if (attempt < maxAttempts) {
            setTimeout(function() {
                waitForWooCommerceBlocks(callback, maxAttempts, attempt + 1);
            }, 500);
        } else {
            console.log('WooCommerce Blocks not found after', maxAttempts, 'attempts');
        }
    }

    // Run SAP pricing update on page load (with delay for WC Blocks)
    waitForWooCommerceBlocks(updateCartWithSapPricing);

    // Re-run when WooCommerce updates the cart
    $(document.body).on('updated_cart_totals updated_wc_div wc_fragments_refreshed', function() {
        console.log('🔄 SAP Debug: Cart updated, refreshing SAP pricing');
        setTimeout(function() {
            waitForWooCommerceBlocks(updateCartWithSapPricing);
        }, 500);
    });

    // Also run when cart quantities change
    $(document).on('change', '.wc-block-components-quantity-selector__input', function() {
        console.log('🔢 SAP Debug: Quantity changed, refreshing SAP pricing');
        setTimeout(function() {
            waitForWooCommerceBlocks(updateCartWithSapPricing);
        }, 1000);
    });
});
